import sys
import asyncio
import os
import time
from pathlib import Path
from dotenv import load_dotenv
# Add project root to path for imports
project_root = str(Path(__file__).parent.parent.parent)
sys.path.append(project_root)

load_dotenv()

from core.state_manager.state_manager import StateManager
from core.logging.logger_config import setup_development_logging, get_module_logger, cleanup_logger
from core.memory.redis_context import RedisClient
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from schemas.a2a_message import A2AMessage, MessageType
from core.config.interrupt_config import InterruptConfig, GlobalInterruptSettings
from core.interruption.interrupt_handler_state import InterruptHandlerState
from core.interruption.action_reversibility import ActionReversibilityDetector
from utils.audio_utils import TTSPlaybackController

logger = get_module_logger("test_state_manager_agents_v2", session_id="test_state_manager_agents_v2")

async def run_trial(sessionId: str = "test_session", userId: str = "user_1"):
    try:
        sm = await StateManager.create("banking_workflow.json", sessionId, userId)
        
        # test 1 - test getting a workflow
        workflow = await sm.getWorkflow()
        print("Workflow:", workflow)

        # test 2 - test getting all pipelines
        pipelines = await sm.getFullPipelineMap()
        print("Pipelines:", pipelines)

        # test 3 - test getting the current workflow state
        current_state = await sm.getCurrentWorkflowState()
        print("Current State:", current_state)

        # test 4 - test getting the current pipeline state
        current_pipeline_state = await sm.getCurrentPipelineState()
        print("Current Pipeline State:", current_pipeline_state)

        # test 5 - test executing a pipeline
        pipeline = await sm.getCurrentPipeline()
        print("Current Pipeline:", pipeline)

        # test 6 - test getting prohibited actions
        prohibited_actions = await sm.getProhibitedActions()
        print("Prohibited Actions:", prohibited_actions)

        # test 7 - test getting allowed actions
        allowed_actions = await sm.getAllowedActions()
        print("Allowed Actions:", allowed_actions)

        # test 8 - execution 

        # Step 1: Simulate greeting state
        
        greeting_tts_input = {"text": "Hello, how can I assist you today?"}
        greeting_tts_result = await sm.executePipelineState(greeting_tts_input)
        print("\ngreeting_tts_result:", greeting_tts_result)
        input("Press Enter to continue...")  # Debug pause

        # Step 2: Simulate state 2
        await sm.transitionWorkflow("Iniquiry")
        iniquiry_stt_input = {"audio_path": "fillerWords/user_conversation_part_1.mp3"}
        iniquiry_stt_result = await sm.executePipelineState(iniquiry_stt_input)
        print("\niniquiry_stt_result:", iniquiry_stt_result)
        input("Press Enter to continue...")  # Debug pause


        # go to the preprocessing state
        await sm.transitionPipeline("preprocessing")
        iniquiry_preprocessing_input = {}
        iniquiry_preprocessing_result = await sm.executePipelineState(iniquiry_preprocessing_input)
        print("\niniquiry_preprocessing_result:", iniquiry_preprocessing_result)
        input("Press Enter to continue...")  # Debug pause


        await sm.transitionPipeline("filler_tts")
        iniquiry_filler_tts_input = {}
        iniquiry_filler_tts_result = await sm.executePipelineState(iniquiry_filler_tts_input)
        print("\niniquiry_filler_tts_result:", iniquiry_filler_tts_result)
        input("Press Enter to continue...")  # Debug pause


        # go to the processing state
        await sm.transitionPipeline("processing")
        iniquiry_processing_input = {}
        iniquiry_processing_result = await sm.executePipelineState(iniquiry_processing_input)
        print("\niniquiry_processing_result:", iniquiry_processing_result)
        input("Press Enter to continue...")  # Debug pause


        # go to the tts state
        await sm.transitionPipeline("tts")
        iniquiry_tts_input = {}
        iniquiry_tts_result = await sm.executePipelineState(iniquiry_tts_input)
        print("\niniquiry_tts_result:", iniquiry_tts_result)
        input("Press Enter to continue...")  # Debug pause

        # Step 2: Simulate state 2
        await sm.transitionWorkflow("CheckBalance")
        check_balance_stt_input = {"audio_path": "fillerWords/user_conversation_part_1.mp3"}
        check_balance_stt_result = await sm.executePipelineState(check_balance_stt_input)
        print("\ncheck_balance_stt_result:", check_balance_stt_result)
        input("Press Enter to continue...")  # Debug pause


        # go to the preprocessing state
        await sm.transitionPipeline("preprocessing")
        check_balance_preprocessing_input = {}
        check_balance_preprocessing_result = await sm.executePipelineState(check_balance_preprocessing_input)
        print("\ncheck_balance_preprocessing_result:", check_balance_preprocessing_result)
        input("Press Enter to continue...")  # Debug pause


        await sm.transitionPipeline("filler_tts")
        check_balance_filler_tts_input = {}
        check_balance_filler_tts_result = await sm.executePipelineState(check_balance_filler_tts_input)
        print("\ncheck_balance_filler_tts_result:", check_balance_filler_tts_result)
        input("Press Enter to continue...")  # Debug pause


        # go to the processing state
        await sm.transitionPipeline("processing")
        check_balance_processing_input = {}
        check_balance_processing_result = await sm.executePipelineState(check_balance_processing_input)
        print("\ncheck_balance_processing_result:", check_balance_processing_result)
        input("Press Enter to continue...")  # Debug pause


        # go to the tts state
        await sm.transitionPipeline("tts")
        check_balance_tts_input = {}
        check_balance_tts_result = await sm.executePipelineState(check_balance_tts_input)
        print("\ncheck_balance_tts_result:", check_balance_tts_result)
        input("Press Enter to continue...")  # Debug pause

        # go to goodbye state
        await sm.transitionWorkflow("Goodbye")
        goodbye_tts_input = {"text": "Thank you for using our service. Goodbye!"}
        goodbye_tts_result = await sm.executePipelineState(goodbye_tts_input)
        print("\ngoodbye_tts_result:", goodbye_tts_result)

    except Exception as e:
        logger.error(f"Error during trial setup: {e}")
        return StateOutput(
            status=StatusType.ERROR,
            message=str(e),
            code=StatusCode.INTERNAL_ERROR,
            outputs={},
            meta={"source": "test_suite"}
        )

async def test_interrupt_detection_during_tts(sessionId: str = "interrupt_test_session", userId: str = "user_interrupt"):
    """Test interrupt detection during TTS playback"""
    try:
        logger.info("🎵 Testing interrupt detection during TTS playback...")

        # Create StateManager with interrupt configuration
        sm = await StateManager.create("banking_workflow.json", sessionId, userId)

        # Create interrupt configuration
        interrupt_config = InterruptConfig(
            global_settings=GlobalInterruptSettings(
                enabled=True,
                vad_threshold=0.01,
                confirmation_window_seconds=0.5,
                min_interrupt_duration_seconds=0.3
            )
        )

        # Test TTS execution with interrupt capability
        await sm.transitionWorkflow("Greeting")
        tts_input = {"text": "This is a long message that can be interrupted by the user speaking"}

        # Execute TTS state
        tts_result = await sm.executePipelineState(tts_input)
        logger.info(f"TTS execution result: {tts_result}")

        # Verify TTS executed successfully
        assert tts_result.status == StatusType.SUCCESS, f"TTS should execute successfully, got {tts_result.status}"

        logger.info("✅ Interrupt detection during TTS test passed")
        return True

    except Exception as e:
        logger.error(f"❌ Interrupt detection test failed: {e}")
        return False

async def test_interrupt_state_transitions(sessionId: str = "interrupt_transition_test", userId: str = "user_transition"):
    """Test state transitions when interrupts occur"""
    try:
        logger.info("🔄 Testing interrupt state transitions...")

        sm = await StateManager.create("banking_workflow.json", sessionId, userId)

        # Get current pipeline map to verify InterruptState is available
        pipelines = await sm.getFullPipelineMap()
        assert "InterruptState" in pipelines, "InterruptState should be available in pipeline map"

        # Test transition to interrupt state
        current_state = await sm.getCurrentWorkflowState()
        logger.info(f"Current workflow state: {current_state}")

        # Simulate interrupt during TTS
        await sm.transitionWorkflow("Greeting")

        # Execute a step that could be interrupted
        greeting_input = {"text": "Hello, welcome to our banking service"}
        result = await sm.executePipelineState(greeting_input)

        logger.info(f"Pipeline execution result: {result}")
        assert result.status == StatusType.SUCCESS, "Pipeline execution should succeed"

        logger.info("✅ Interrupt state transitions test passed")
        return True

    except Exception as e:
        logger.error(f"❌ Interrupt state transitions test failed: {e}")
        return False

async def test_reversible_vs_irreversible_actions(sessionId: str = "reversibility_test", userId: str = "user_reversibility"):
    """Test reversible vs irreversible action handling during interrupts"""
    try:
        logger.info("⚖️ Testing reversible vs irreversible action handling...")

        sm = await StateManager.create("banking_workflow.json", sessionId, userId)

        # Test reversible action (informational query)
        await sm.transitionWorkflow("CheckBalance")
        balance_input = {"audio_path": "test_audio.mp3"}

        # Execute balance check (reversible action)
        balance_result = await sm.executePipelineState(balance_input)
        logger.info(f"Balance check result: {balance_result}")

        # Test irreversible action (fund transfer)
        await sm.transitionWorkflow("FundTransfer")
        transfer_input = {"audio_path": "test_transfer.mp3"}

        # Execute fund transfer (irreversible action)
        transfer_result = await sm.executePipelineState(transfer_input)
        logger.info(f"Fund transfer result: {transfer_result}")

        logger.info("✅ Reversible vs irreversible actions test passed")
        return True

    except Exception as e:
        logger.error(f"❌ Reversible vs irreversible actions test failed: {e}")
        return False

async def test_memory_management_during_interrupts(sessionId: str = "memory_test", userId: str = "user_memory"):
    """Test memory management during interrupt scenarios"""
    try:
        logger.info("🧠 Testing memory management during interrupts...")

        sm = await StateManager.create("banking_workflow.json", sessionId, userId)

        # Store some context in memory before interrupt
        await sm.transitionWorkflow("Greeting")
        greeting_input = {"text": "Hello, I need help with my account"}

        # Execute and verify memory state
        result = await sm.executePipelineState(greeting_input)
        logger.info(f"Greeting result: {result}")

        # Check that memory is properly managed
        current_state = await sm.getCurrentWorkflowState()
        logger.info(f"Current state after greeting: {current_state}")

        # Verify memory persistence
        assert result.status == StatusType.SUCCESS, "Memory should be properly managed during interrupts"

        logger.info("✅ Memory management during interrupts test passed")
        return True

    except Exception as e:
        logger.error(f"❌ Memory management test failed: {e}")
        return False

async def test_resume_functionality_after_interrupts(sessionId: str = "resume_test", userId: str = "user_resume"):
    """Test resume functionality after interrupt handling"""
    try:
        logger.info("▶️ Testing resume functionality after interrupts...")

        sm = await StateManager.create("banking_workflow.json", sessionId, userId)

        # Start a process that can be interrupted and resumed
        await sm.transitionWorkflow("Greeting")
        inquiry_input = {"audio_path": "test_inquiry.mp3"}

        # Execute inquiry process
        inquiry_result = await sm.executePipelineState(inquiry_input)
        logger.info(f"Inquiry result: {inquiry_result}")

        # Test pipeline state transitions (simulating resume)
        await sm.transitionPipeline("preprocessing")
        preprocessing_result = await sm.executePipelineState({})
        logger.info(f"Preprocessing result: {preprocessing_result}")

        # Continue with the pipeline
        await sm.transitionPipeline("processing")
        processing_result = await sm.executePipelineState({})
        logger.info(f"Processing result: {processing_result}")

        logger.info("✅ Resume functionality test passed")
        return True

    except Exception as e:
        logger.error(f"❌ Resume functionality test failed: {e}")
        return False

async def run_interrupt_tests():
    """Run all interrupt handling tests"""
    logger.info("🚀 Starting Interrupt Handling Integration Tests")
    logger.info("=" * 70)

    test_results = {}

    # Run all interrupt tests
    test_functions = [
        ("Interrupt Detection During TTS", test_interrupt_detection_during_tts),
        ("Interrupt State Transitions", test_interrupt_state_transitions),
        ("Reversible vs Irreversible Actions", test_reversible_vs_irreversible_actions),
        ("Memory Management During Interrupts", test_memory_management_during_interrupts),
        ("Resume Functionality After Interrupts", test_resume_functionality_after_interrupts)
    ]

    for test_name, test_func in test_functions:
        logger.info(f"\n📋 Running: {test_name}")
        try:
            result = await test_func()
            test_results[test_name] = result
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.info(f"❌ {test_name}: FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            test_results[test_name] = False

    # Print summary
    logger.info("\n" + "=" * 70)
    logger.info("📊 INTERRUPT TESTS SUMMARY")
    logger.info("=" * 70)

    passed = sum(1 for result in test_results.values() if result)
    total = len(test_results)

    for test_name, result in test_results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")

    logger.info(f"\nOverall: {passed}/{total} tests passed")

    if passed == total:
        logger.info("🎉 All interrupt handling tests passed!")
        return True
    else:
        logger.info("⚠️ Some interrupt handling tests failed.")
        return False

if __name__ == "__main__":
    try:
        setup_development_logging()

        # Ask user which tests to run
        print("\nSelect test mode:")
        print("1. Run original StateManager tests (interactive)")
        print("2. Run interrupt handling tests (automated)")
        print("3. Run both test suites")

        choice = input("Enter your choice (1-3): ").strip()

        if choice == "1":
            print("\n🔧 Running original StateManager tests...")
            asyncio.run(run_trial())
        elif choice == "2":
            print("\n🎯 Running interrupt handling tests...")
            asyncio.run(run_interrupt_tests())
        elif choice == "3":
            print("\n🚀 Running both test suites...")
            print("\n" + "="*50)
            print("PART 1: INTERRUPT HANDLING TESTS")
            print("="*50)
            interrupt_success = asyncio.run(run_interrupt_tests())

            print("\n" + "="*50)
            print("PART 2: ORIGINAL STATEMANAGER TESTS")
            print("="*50)
            print("Note: Original tests are interactive - press Enter to continue through each step")
            input("Press Enter to start original tests...")
            asyncio.run(run_trial())

            if interrupt_success:
                print("\n🎉 All interrupt tests passed! Original tests completed.")
            else:
                print("\n⚠️ Some interrupt tests failed. Check logs for details.")
        else:
            print("Invalid choice. Running interrupt tests by default...")
            asyncio.run(run_interrupt_tests())

    except Exception as e:
        logger.error(f"Error in main execution: {e}")
    finally:
        cleanup_logger()  # Ensure logger is cleaned up properly

